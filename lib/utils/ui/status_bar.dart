import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:dasso_reader/config/design_system_extensions.dart';

/// Enhanced status bar utilities with DesignSystem integration
///
/// Provides both functional control (show/hide) and styling (theme-aware)
/// for a professional, consistent status bar experience across all devices.

// =====================================================
// FUNCTIONAL STATUS BAR CONTROL (Existing functionality preserved)
// =====================================================

/// Hides the status bar completely for immersive experience
void hideStatusBar() {
  SystemChrome.setEnabledSystemUIMode(
    SystemUiMode.manual,
    overlays: [],
  );
}

/// Shows the status bar with all system overlays
void showStatusBar() {
  SystemChrome.setEnabledSystemUIMode(
    SystemUiMode.manual,
    overlays: SystemUiOverlay.values,
  );
}

/// Shows status bar without resizing the app content (edge-to-edge)
void showStatusBarWithoutResize() {
  SystemChrome.setEnabledSystemUIMode(
    SystemUiMode.edgeToEdge,
  );
}

/// Shows only the status bar (no navigation bar)
void onlyStatusBar() {
  SystemChrome.setEnabledSystemUIMode(
    SystemUiMode.manual,
    overlays: [
      SystemUiOverlay.top,
    ],
  );
}

// =====================================================
// ENHANCED STATUS BAR STYLING (New DesignSystem integration)
// =====================================================

/// Applies theme-aware status bar styling
///
/// Automatically adapts status bar icons and colors based on the current theme.
/// Should be called when the app starts or theme changes.
void applyAdaptiveStatusBarStyle(BuildContext context) {
  StatusBarDesign.applyAdaptiveStyle(context);
}

/// Applies status bar styling optimized for reading experience
///
/// [isImmersive] - Whether to use full immersive mode
/// [backgroundColor] - Optional background color to optimize icon visibility
void applyReadingStatusBarStyle(
  BuildContext context, {
  required bool isImmersive,
  Color? backgroundColor,
}) {
  StatusBarDesign.applyReadingStyle(
    context,
    isImmersive: isImmersive,
    backgroundColor: backgroundColor,
  );
}

/// Shows status bar with proper theme-aware styling
///
/// Enhanced version of showStatusBar() that also applies appropriate styling
void showStyledStatusBar(BuildContext context) {
  showStatusBar();
  applyAdaptiveStatusBarStyle(context);
}

/// Shows status bar without resize and applies theme-aware styling
///
/// Enhanced version of showStatusBarWithoutResize() with styling
void showStyledStatusBarWithoutResize(BuildContext context) {
  showStatusBarWithoutResize();
  applyAdaptiveStatusBarStyle(context);
}

/// Hides status bar and applies immersive styling
///
/// Enhanced version of hideStatusBar() for reading mode
void hideStatusBarForReading(BuildContext context) {
  hideStatusBar();
  applyReadingStatusBarStyle(context, isImmersive: true);
}
